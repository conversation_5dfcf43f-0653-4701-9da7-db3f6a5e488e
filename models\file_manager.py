"""
File management models for the Document Management System.
Handles file operations, metadata management, and storage.
"""

import os
import shutil
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import mimetypes
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileManager:
    """Manages file operations and storage for the DMS."""
    
    # Supported file types
    SUPPORTED_EXTENSIONS = {
        '.pdf': 'PDF Document',
        '.doc': 'Word Document',
        '.docx': 'Word Document',
        '.jpg': 'JPEG Image',
        '.jpeg': 'JPEG Image',
        '.png': 'PNG Image',
        '.gif': 'GIF Image',
        '.bmp': 'Bitmap Image'
    }
    
    def __init__(self, storage_path: str = "storage"):
        """
        Initialize file manager.
        
        Args:
            storage_path: Path to the storage directory
        """
        self.storage_path = os.path.abspath(storage_path)
        self.ensure_storage_directory()
    
    def ensure_storage_directory(self) -> None:
        """Ensure the storage directory exists."""
        try:
            os.makedirs(self.storage_path, exist_ok=True)
            logger.info(f"Storage directory ensured: {self.storage_path}")
        except OSError as e:
            logger.error(f"Error creating storage directory: {e}")
            raise
    
    def is_supported_file(self, file_path: str) -> bool:
        """
        Check if a file type is supported.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file type is supported, False otherwise
        """
        _, ext = os.path.splitext(file_path.lower())
        return ext in self.SUPPORTED_EXTENSIONS
    
    def get_file_type_description(self, file_path: str) -> str:
        """
        Get a human-readable description of the file type.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File type description
        """
        _, ext = os.path.splitext(file_path.lower())
        return self.SUPPORTED_EXTENSIONS.get(ext, 'Unknown File Type')
    
    def get_file_info(self, file_path: str) -> Dict:
        """
        Get comprehensive information about a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary containing file information
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            stat = os.stat(file_path)
            _, ext = os.path.splitext(file_path)
            
            return {
                'name': os.path.basename(file_path),
                'size': stat.st_size,
                'extension': ext.lower(),
                'type_description': self.get_file_type_description(file_path),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'created_time': datetime.fromtimestamp(stat.st_ctime),
                'is_supported': self.is_supported_file(file_path)
            }
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {e}")
            raise
    
    def generate_unique_filename(self, original_filename: str) -> str:
        """
        Generate a unique filename to avoid conflicts.
        
        Args:
            original_filename: Original filename
            
        Returns:
            Unique filename with timestamp
        """
        name, ext = os.path.splitext(original_filename)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{name}_{timestamp}{ext}"
    
    def calculate_file_hash(self, file_path: str) -> str:
        """
        Calculate MD5 hash of a file for integrity checking.
        
        Args:
            file_path: Path to the file
            
        Returns:
            MD5 hash string
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating hash for {file_path}: {e}")
            return ""
    
    def store_file(self, source_path: str, metadata: Dict) -> Dict:
        """
        Store a file in the storage directory with metadata.
        
        Args:
            source_path: Path to the source file
            metadata: File metadata dictionary
            
        Returns:
            Dictionary with storage information
        """
        try:
            # Validate file
            if not os.path.exists(source_path):
                raise FileNotFoundError(f"Source file not found: {source_path}")
            
            if not self.is_supported_file(source_path):
                raise ValueError(f"Unsupported file type: {source_path}")
            
            # Get file info
            file_info = self.get_file_info(source_path)
            
            # Generate unique filename
            original_name = os.path.basename(source_path)
            unique_name = self.generate_unique_filename(original_name)
            
            # Create destination path
            dest_path = os.path.join(self.storage_path, unique_name)
            
            # Copy file to storage
            shutil.copy2(source_path, dest_path)
            
            # Calculate file hash for integrity
            file_hash = self.calculate_file_hash(dest_path)
            
            # Prepare storage info
            storage_info = {
                'original_name': original_name,
                'stored_name': unique_name,
                'storage_path': dest_path,
                'relative_path': os.path.relpath(dest_path),
                'file_size': file_info['size'],
                'file_type': file_info['extension'],
                'type_description': file_info['type_description'],
                'file_hash': file_hash,
                'stored_at': datetime.now()
            }
            
            logger.info(f"File stored successfully: {unique_name}")
            return storage_info
            
        except Exception as e:
            logger.error(f"Error storing file {source_path}: {e}")
            raise
    
    def delete_stored_file(self, file_path: str) -> bool:
        """
        Delete a file from storage.
        
        Args:
            file_path: Path to the stored file
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"File deleted: {file_path}")
                return True
            else:
                logger.warning(f"File not found for deletion: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            return False
    
    def get_storage_stats(self) -> Dict:
        """
        Get statistics about the storage directory.
        
        Returns:
            Dictionary with storage statistics
        """
        try:
            total_files = 0
            total_size = 0
            file_types = {}
            
            if os.path.exists(self.storage_path):
                for filename in os.listdir(self.storage_path):
                    file_path = os.path.join(self.storage_path, filename)
                    if os.path.isfile(file_path):
                        total_files += 1
                        file_size = os.path.getsize(file_path)
                        total_size += file_size
                        
                        _, ext = os.path.splitext(filename.lower())
                        file_types[ext] = file_types.get(ext, 0) + 1
            
            return {
                'total_files': total_files,
                'total_size': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'file_types': file_types,
                'storage_path': self.storage_path
            }
            
        except Exception as e:
            logger.error(f"Error getting storage stats: {e}")
            return {
                'total_files': 0,
                'total_size': 0,
                'total_size_mb': 0,
                'file_types': {},
                'storage_path': self.storage_path
            }

class DocumentMetadata:
    """Handles document metadata operations."""
    
    def __init__(self):
        """Initialize metadata handler."""
        pass
    
    @staticmethod
    def validate_metadata(metadata: Dict) -> Tuple[bool, List[str]]:
        """
        Validate document metadata.
        
        Args:
            metadata: Metadata dictionary to validate
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []
        
        # Required fields
        required_fields = ['title', 'category']
        for field in required_fields:
            if not metadata.get(field, '').strip():
                errors.append(f"{field.title()} is required")
        
        # Title length validation
        title = metadata.get('title', '')
        if len(title) > 255:
            errors.append("Title must be less than 255 characters")
        
        # Description length validation
        description = metadata.get('description', '')
        if len(description) > 1000:
            errors.append("Description must be less than 1000 characters")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def sanitize_metadata(metadata: Dict) -> Dict:
        """
        Sanitize and clean metadata.
        
        Args:
            metadata: Raw metadata dictionary
            
        Returns:
            Sanitized metadata dictionary
        """
        sanitized = {}
        
        # Clean string fields
        string_fields = ['title', 'description', 'category']
        for field in string_fields:
            value = metadata.get(field, '')
            if isinstance(value, str):
                sanitized[field] = value.strip()
            else:
                sanitized[field] = str(value).strip()
        
        return sanitized
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        Format file size in human-readable format.
        
        Args:
            size_bytes: File size in bytes
            
        Returns:
            Formatted file size string
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
