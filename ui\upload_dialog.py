"""
Upload dialog for the Document Management System.
Handles file selection, metadata capture, and file upload process.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from typing import Dict, Optional
import logging

from db.database import DatabaseManager
from models.file_manager import FileManager, DocumentMetadata

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UploadDialog:
    """Dialog for uploading documents with metadata."""
    
    def __init__(self, parent, db_manager: DatabaseManager, file_manager: FileManager):
        """
        Initialize the upload dialog.
        
        Args:
            parent: Parent window
            db_manager: Database manager instance
            file_manager: File manager instance
        """
        self.parent = parent
        self.db_manager = db_manager
        self.file_manager = file_manager
        self.document_metadata = DocumentMetadata()
        
        # Dialog result
        self.result = None
        self.selected_file_path = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.setup_dialog()
        self.create_widgets()
        
        # Show dialog
        self.dialog.transient(parent)
        self.dialog.grab_set()
        self.dialog.focus_set()
        
        # Wait for dialog to close
        parent.wait_window(self.dialog)
    
    def setup_dialog(self) -> None:
        """Configure the dialog window properties."""
        self.dialog.title("Upload Document")
        self.dialog.geometry("600x500")
        self.dialog.resizable(False, False)
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.cancel_upload)
    
    def create_widgets(self) -> None:
        """Create and layout all dialog widgets."""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Upload New Document", 
                               font=('Segoe UI', 14, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # File selection section
        file_frame = ttk.LabelFrame(main_frame, text="File Selection", padding="15")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="Selected File:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        self.file_path_var = tk.StringVar(value="No file selected")
        self.file_path_label = ttk.Label(file_frame, textvariable=self.file_path_var, 
                                        foreground="gray", font=('Segoe UI', 9))
        self.file_path_label.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.browse_btn = ttk.Button(file_frame, text="Browse Files", 
                                    command=self.browse_file)
        self.browse_btn.grid(row=2, column=0, sticky=tk.W)
        
        # File info display
        self.file_info_var = tk.StringVar()
        self.file_info_label = ttk.Label(file_frame, textvariable=self.file_info_var, 
                                        font=('Segoe UI', 9), foreground="blue")
        self.file_info_label.grid(row=2, column=1, sticky=tk.E, padx=(10, 0))
        
        # Metadata section
        metadata_frame = ttk.LabelFrame(main_frame, text="Document Information", padding="15")
        metadata_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        metadata_frame.columnconfigure(1, weight=1)
        
        # Title field
        ttk.Label(metadata_frame, text="Title *:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.title_var = tk.StringVar()
        self.title_entry = ttk.Entry(metadata_frame, textvariable=self.title_var, font=('Segoe UI', 10))
        self.title_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 10), padx=(10, 0))
        
        # Description field
        ttk.Label(metadata_frame, text="Description:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=(0, 5))
        
        desc_frame = ttk.Frame(metadata_frame)
        desc_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 10), padx=(10, 0))
        desc_frame.columnconfigure(0, weight=1)
        
        self.description_text = tk.Text(desc_frame, height=4, width=40, font=('Segoe UI', 9),
                                       wrap=tk.WORD)
        desc_scrollbar = ttk.Scrollbar(desc_frame, orient=tk.VERTICAL, 
                                      command=self.description_text.yview)
        self.description_text.configure(yscrollcommand=desc_scrollbar.set)
        
        self.description_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        desc_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Category field
        ttk.Label(metadata_frame, text="Category *:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(metadata_frame, textvariable=self.category_var,
                                          font=('Segoe UI', 10), state="readonly")
        self.category_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 10), padx=(10, 0))
        
        # Load categories
        self.load_categories()
        
        # Required fields note
        note_label = ttk.Label(metadata_frame, text="* Required fields", 
                              font=('Segoe UI', 8), foreground="red")
        note_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
        
        # Progress section (initially hidden)
        self.progress_frame = ttk.LabelFrame(main_frame, text="Upload Progress", padding="15")
        self.progress_var = tk.StringVar(value="Ready to upload...")
        self.progress_label = ttk.Label(self.progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        self.progress_frame.columnconfigure(0, weight=1)
        
        # Buttons section
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(15, 0))
        
        self.cancel_btn = ttk.Button(button_frame, text="Cancel", 
                                    command=self.cancel_upload)
        self.cancel_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.upload_btn = ttk.Button(button_frame, text="Upload Document", 
                                    command=self.upload_document,
                                    style='Primary.TButton')
        self.upload_btn.grid(row=0, column=1)
        
        # Initially disable upload button
        self.upload_btn.configure(state='disabled')
        
        # Bind events
        self.title_var.trace('w', self.validate_form)
        self.category_var.trace('w', self.validate_form)
    
    def load_categories(self) -> None:
        """Load available categories from the database."""
        try:
            categories = self.db_manager.get_categories()
            category_names = [cat['name'] for cat in categories]
            
            self.category_combo['values'] = category_names
            
            # Set default category
            if category_names:
                self.category_combo.set(category_names[0])
                
        except Exception as e:
            logger.error(f"Error loading categories: {e}")
            # Fallback categories
            self.category_combo['values'] = ['General', 'Reports', 'Contracts', 'Images', 'Archive']
            self.category_combo.set('General')
    
    def browse_file(self) -> None:
        """Open file browser to select a document."""
        # Define supported file types
        filetypes = [
            ("All Supported", "*.pdf;*.doc;*.docx;*.jpg;*.jpeg;*.png;*.gif;*.bmp"),
            ("PDF Documents", "*.pdf"),
            ("Word Documents", "*.doc;*.docx"),
            ("Image Files", "*.jpg;*.jpeg;*.png;*.gif;*.bmp"),
            ("All Files", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="Select Document to Upload",
            filetypes=filetypes,
            parent=self.dialog
        )
        
        if file_path:
            self.selected_file_path = file_path
            self.update_file_display(file_path)
            self.auto_fill_title(file_path)
            self.validate_form()
    
    def update_file_display(self, file_path: str) -> None:
        """Update the file display with selected file information."""
        try:
            # Check if file is supported
            if not self.file_manager.is_supported_file(file_path):
                self.file_path_var.set("Unsupported file type!")
                self.file_path_label.configure(foreground="red")
                self.file_info_var.set("")
                self.selected_file_path = None
                return
            
            # Get file info
            file_info = self.file_manager.get_file_info(file_path)
            
            # Update display
            filename = os.path.basename(file_path)
            self.file_path_var.set(filename)
            self.file_path_label.configure(foreground="black")
            
            # Show file info
            size_str = self.document_metadata.format_file_size(file_info['size'])
            info_text = f"{file_info['type_description']} • {size_str}"
            self.file_info_var.set(info_text)
            
        except Exception as e:
            logger.error(f"Error updating file display: {e}")
            self.file_path_var.set("Error reading file")
            self.file_path_label.configure(foreground="red")
            self.file_info_var.set("")
            self.selected_file_path = None
    
    def auto_fill_title(self, file_path: str) -> None:
        """Auto-fill the title field based on the filename."""
        if not self.title_var.get().strip():  # Only if title is empty
            filename = os.path.basename(file_path)
            title = os.path.splitext(filename)[0]  # Remove extension
            # Clean up the title
            title = title.replace('_', ' ').replace('-', ' ')
            title = ' '.join(word.capitalize() for word in title.split())
            self.title_var.set(title)
    
    def validate_form(self, *args) -> None:
        """Validate the form and enable/disable upload button."""
        is_valid = (
            self.selected_file_path and
            self.title_var.get().strip() and
            self.category_var.get().strip()
        )
        
        state = 'normal' if is_valid else 'disabled'
        self.upload_btn.configure(state=state)
    
    def show_progress(self, show: bool = True) -> None:
        """Show or hide the progress section."""
        if show:
            self.progress_frame.grid(row=3, column=0, columnspan=2, 
                                   sticky=(tk.W, tk.E), pady=(0, 15))
            self.progress_bar.start()
        else:
            self.progress_frame.grid_remove()
            self.progress_bar.stop()
    
    def upload_document(self) -> None:
        """Upload the selected document with metadata."""
        if not self.selected_file_path:
            messagebox.showerror("Error", "Please select a file to upload.")
            return
        
        # Validate metadata
        metadata = {
            'title': self.title_var.get().strip(),
            'description': self.description_text.get('1.0', tk.END).strip(),
            'category': self.category_var.get().strip()
        }
        
        is_valid, errors = self.document_metadata.validate_metadata(metadata)
        if not is_valid:
            messagebox.showerror("Validation Error", "\n".join(errors))
            return
        
        # Sanitize metadata
        metadata = self.document_metadata.sanitize_metadata(metadata)
        
        try:
            # Show progress
            self.show_progress(True)
            self.progress_var.set("Storing file...")
            self.dialog.update()
            
            # Disable buttons during upload
            self.upload_btn.configure(state='disabled')
            self.browse_btn.configure(state='disabled')
            
            # Store file
            storage_info = self.file_manager.store_file(self.selected_file_path, metadata)
            
            self.progress_var.set("Saving to database...")
            self.dialog.update()
            
            # Add to database
            document_id = self.db_manager.add_document(
                title=metadata['title'],
                description=metadata['description'],
                category=metadata['category'],
                file_name=storage_info['original_name'],
                file_path=storage_info['storage_path'],
                file_size=storage_info['file_size'],
                file_type=storage_info['file_type']
            )
            
            self.progress_var.set("Upload completed!")
            self.dialog.update()
            
            # Set result and close
            self.result = {
                'document_id': document_id,
                'storage_info': storage_info,
                'metadata': metadata
            }
            
            logger.info(f"Document uploaded successfully: {metadata['title']}")
            
            # Close dialog after a brief delay
            self.dialog.after(1000, self.dialog.destroy)
            
        except Exception as e:
            logger.error(f"Error uploading document: {e}")
            self.show_progress(False)
            
            # Re-enable buttons
            self.upload_btn.configure(state='normal')
            self.browse_btn.configure(state='normal')
            
            messagebox.showerror("Upload Error", f"Failed to upload document:\n{str(e)}")
    
    def cancel_upload(self) -> None:
        """Cancel the upload and close dialog."""
        self.result = None
        self.dialog.destroy()
