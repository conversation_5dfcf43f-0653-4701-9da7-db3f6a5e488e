#!/usr/bin/env python3
"""
Test script for the Document Management System.
Creates sample data and tests core functionality.
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from db.database import DatabaseManager
from models.file_manager import FileManager, DocumentMetadata

def create_sample_files():
    """Create sample files for testing."""
    sample_files = []
    
    # Create a temporary directory for sample files
    temp_dir = tempfile.mkdtemp(prefix="dms_test_")
    
    # Create sample text files (simulating different document types)
    samples = [
        ("sample_report.pdf", "This is a sample PDF report content."),
        ("contract_agreement.doc", "This is a sample Word document content."),
        ("project_image.jpg", "This is a sample image file content."),
        ("meeting_notes.docx", "This is a sample meeting notes document."),
    ]
    
    for filename, content in samples:
        file_path = os.path.join(temp_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        sample_files.append(file_path)
    
    return temp_dir, sample_files

def test_database_operations():
    """Test database operations."""
    print("Testing database operations...")
    
    try:
        # Initialize database
        db = DatabaseManager("test_dms.db")
        
        # Test adding a document
        doc_id = db.add_document(
            title="Test Document",
            description="This is a test document",
            category="General",
            file_name="test.pdf",
            file_path="/path/to/test.pdf",
            file_size=1024,
            file_type=".pdf"
        )
        
        print(f"✓ Document added with ID: {doc_id}")
        
        # Test retrieving documents
        documents = db.get_all_documents()
        print(f"✓ Retrieved {len(documents)} documents")
        
        # Test searching documents
        results = db.search_documents("test")
        print(f"✓ Search found {len(results)} documents")
        
        # Test getting categories
        categories = db.get_categories()
        print(f"✓ Found {len(categories)} categories")
        
        # Test statistics
        stats = db.get_document_stats()
        print(f"✓ Statistics: {stats['total_documents']} total documents")
        
        # Clean up test database
        if os.path.exists("test_dms.db"):
            os.remove("test_dms.db")
        
        print("✓ Database operations test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Database operations test failed: {e}")
        return False

def test_file_operations():
    """Test file management operations."""
    print("\nTesting file operations...")
    
    try:
        # Create sample files
        temp_dir, sample_files = create_sample_files()
        
        # Initialize file manager with test storage
        test_storage = tempfile.mkdtemp(prefix="dms_storage_")
        file_manager = FileManager(test_storage)
        
        print(f"✓ Created test storage: {test_storage}")
        
        # Test file info
        if sample_files:
            file_info = file_manager.get_file_info(sample_files[0])
            print(f"✓ File info retrieved: {file_info['name']}")
        
        # Test file type checking
        supported = file_manager.is_supported_file("test.pdf")
        print(f"✓ File type check: PDF supported = {supported}")
        
        # Test storage stats
        stats = file_manager.get_storage_stats()
        print(f"✓ Storage stats: {stats['total_files']} files")
        
        # Clean up
        shutil.rmtree(temp_dir, ignore_errors=True)
        shutil.rmtree(test_storage, ignore_errors=True)
        
        print("✓ File operations test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ File operations test failed: {e}")
        return False

def test_metadata_operations():
    """Test metadata operations."""
    print("\nTesting metadata operations...")
    
    try:
        metadata_handler = DocumentMetadata()
        
        # Test metadata validation
        valid_metadata = {
            'title': 'Test Document',
            'description': 'A test document',
            'category': 'General'
        }
        
        is_valid, errors = metadata_handler.validate_metadata(valid_metadata)
        print(f"✓ Valid metadata check: {is_valid}")
        
        # Test invalid metadata
        invalid_metadata = {
            'title': '',  # Empty title should be invalid
            'description': 'A test document',
            'category': 'General'
        }
        
        is_valid, errors = metadata_handler.validate_metadata(invalid_metadata)
        print(f"✓ Invalid metadata check: {not is_valid} (should be False)")
        
        # Test metadata sanitization
        dirty_metadata = {
            'title': '  Test Document  ',
            'description': '  A test document  ',
            'category': '  General  '
        }
        
        clean_metadata = metadata_handler.sanitize_metadata(dirty_metadata)
        print(f"✓ Metadata sanitization: '{clean_metadata['title']}'")
        
        # Test file size formatting
        size_str = metadata_handler.format_file_size(1024)
        print(f"✓ File size formatting: {size_str}")
        
        print("✓ Metadata operations test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Metadata operations test failed: {e}")
        return False

def test_application_startup():
    """Test application startup without GUI."""
    print("\nTesting application startup...")
    
    try:
        # Test imports
        from ui.main_window import MainWindow
        from ui.upload_dialog import UploadDialog
        from ui.search_dialog import SearchDialog
        
        print("✓ All UI modules imported successfully")
        
        # Test database initialization
        db = DatabaseManager()
        categories = db.get_categories()
        print(f"✓ Database initialized with {len(categories)} categories")
        
        # Test file manager initialization
        file_manager = FileManager()
        stats = file_manager.get_storage_stats()
        print(f"✓ File manager initialized, storage path: {stats['storage_path']}")
        
        print("✓ Application startup test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Application startup test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Document Management System - Test Suite")
    print("=" * 50)
    
    tests = [
        test_database_operations,
        test_file_operations,
        test_metadata_operations,
        test_application_startup
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to use.")
        print("\nTo start the application, run:")
        print("python main.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
