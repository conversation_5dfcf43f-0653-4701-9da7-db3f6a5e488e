#!/usr/bin/env python3
"""
Document Management System (DMS) - Main Application Entry Point

A complete desktop application for managing electronic documents with features including:
- File upload with metadata capture
- Search functionality by title and description
- File management with view/open and delete operations
- SQLite database for metadata storage
- Support for PDF, Word documents, and image files

Author: Augment Agent
Date: 2025-09-05
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dms.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if all required dependencies are available."""
    try:
        import tkinter
        import sqlite3
        logger.info("All dependencies are available")
        return True
    except ImportError as e:
        logger.error(f"Missing dependency: {e}")
        print(f"Error: Missing required dependency - {e}")
        print("Please ensure Python is installed with tkinter support.")
        return False

def setup_application_directories():
    """Ensure all required directories exist."""
    directories = ['storage', 'db', 'ui', 'models']
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)
        logger.info(f"Directory ensured: {dir_path}")

def main():
    """Main application entry point."""
    try:
        logger.info("Starting Document Management System")
        
        # Check dependencies
        if not check_dependencies():
            return 1
        
        # Setup directories
        setup_application_directories()
        
        # Import and start the main application
        from ui.main_window import MainWindow
        
        # Create and run the application
        app = MainWindow()
        app.run()
        
        logger.info("Document Management System closed normally")
        return 0
        
    except ImportError as e:
        logger.error(f"Import error: {e}")
        print(f"Error: Failed to import required modules - {e}")
        print("Please ensure all application files are present.")
        return 1
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"An unexpected error occurred: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
