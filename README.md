# Document Management System (DMS)

A complete desktop application for managing electronic documents built with Python and Tkinter.

## Features

### Core Functionality
- **File Upload**: Support for PDF, Word documents (.doc, .docx), and images (.jpg, .png, .gif, .bmp)
- **Metadata Management**: Capture title, description, date, and category for each document
- **Search Functionality**: Search by title and description with partial text matching
- **File Management**: View, open, and delete documents with an intuitive interface
- **Database Storage**: SQLite database for reliable metadata storage

### User Interface
- Modern, clean interface using ttk themed widgets
- Responsive layout that works on different screen sizes
- Elegant file list with sortable columns
- Progress indicators for file operations
- Detailed document information dialogs

### Technical Features
- Organized code structure with separation of concerns
- Comprehensive error handling and logging
- File integrity checking with MD5 hashing
- Automatic file organization in storage directory
- Database connection management and optimization

## Project Structure

```
DMS/
├── main.py                 # Main application entry point
├── ui/                     # User interface components
│   ├── __init__.py
│   ├── main_window.py      # Main application window
│   ├── upload_dialog.py    # File upload dialog
│   └── search_dialog.py    # Advanced search dialog
├── db/                     # Database operations
│   ├── __init__.py
│   └── database.py         # SQLite database manager
├── models/                 # File management models
│   ├── __init__.py
│   └── file_manager.py     # File operations and metadata
├── storage/                # Document storage directory
├── dms.db                  # SQLite database (created automatically)
├── dms.log                 # Application log file
└── README.md               # This file
```

## Requirements

- **Python 3.8 or higher**
- **tkinter** (usually included with Python)
- **sqlite3** (included with Python standard library)

No additional packages need to be installed - the application uses only Python standard library modules.

## Installation & Setup

1. **Download/Clone the project**:
   ```bash
   # If using git
   git clone <repository-url>
   cd DMS
   
   # Or extract the project files to a directory
   ```

2. **Verify Python installation**:
   ```bash
   python --version
   # Should show Python 3.8 or higher
   ```

3. **Test tkinter availability**:
   ```bash
   python -c "import tkinter; print('tkinter is available')"
   ```

## Running the Application

### Windows
```bash
python main.py
```

### macOS/Linux
```bash
python3 main.py
```

The application will:
1. Create necessary directories automatically
2. Initialize the SQLite database with default categories
3. Open the main application window

## Usage Guide

### Uploading Documents

1. Click **"Upload Document"** button in the main window
2. Click **"Browse Files"** to select a document
3. Fill in the required metadata:
   - **Title**: Document title (required)
   - **Description**: Optional description
   - **Category**: Select from predefined categories (required)
4. Click **"Upload Document"** to complete the process

**Supported File Types**:
- PDF Documents (`.pdf`)
- Word Documents (`.doc`, `.docx`)
- Images (`.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`)

### Searching Documents

#### Quick Search
1. Enter search terms in the search box on the left panel
2. Click **"Search"** or press Enter
3. Results will be filtered in the main document list

#### Advanced Search
1. Use the search dialog for more detailed filtering
2. Search by title, description, category, and file type
3. View results in a dedicated results window

### Managing Documents

#### Viewing Documents
- **Double-click** any document in the list to open it
- Or select a document and click **"View/Open"**
- Documents open with the system's default application

#### Document Details
- Select a document and click **"Details"** to view comprehensive information
- Shows metadata, file information, and storage details

#### Deleting Documents
- Select a document and click **"Delete"**
- Confirm the deletion in the dialog
- Both database record and physical file are removed

### Categories

Default categories include:
- **General**: General documents
- **Reports**: Business reports and analytics
- **Contracts**: Legal contracts and agreements
- **Images**: Image files and graphics
- **Archive**: Archived documents

## Database Schema

The application uses SQLite with the following main tables:

### Documents Table
- `id`: Primary key
- `title`: Document title
- `description`: Document description
- `category`: Document category
- `file_name`: Original filename
- `file_path`: Storage path
- `file_size`: File size in bytes
- `file_type`: File extension
- `upload_date`: Upload timestamp
- `created_at`: Record creation time
- `updated_at`: Last update time

### Categories Table
- `id`: Primary key
- `name`: Category name
- `description`: Category description
- `created_at`: Creation timestamp

## File Storage

- Documents are stored in the `storage/` directory
- Files are renamed with timestamps to avoid conflicts
- Original filenames are preserved in the database
- File integrity is verified using MD5 hashing

## Logging

The application logs important events to:
- **Console**: Real-time feedback during operation
- **dms.log**: Persistent log file for troubleshooting

Log levels include INFO, WARNING, and ERROR messages.

## Troubleshooting

### Common Issues

1. **"tkinter not found" error**:
   - On Ubuntu/Debian: `sudo apt-get install python3-tk`
   - On CentOS/RHEL: `sudo yum install tkinter`
   - On macOS: tkinter should be included with Python

2. **Permission errors**:
   - Ensure write permissions in the application directory
   - Check that the storage directory can be created

3. **Database errors**:
   - Delete `dms.db` to reset the database
   - Check disk space availability

4. **File not opening**:
   - Ensure appropriate applications are installed for file types
   - Check file permissions and existence

### Getting Help

Check the log file (`dms.log`) for detailed error information. The application provides user-friendly error messages for common issues.

## Development

### Code Structure
- **Separation of Concerns**: UI, database, and file operations are separated
- **Error Handling**: Comprehensive exception handling throughout
- **Logging**: Detailed logging for debugging and monitoring
- **Extensibility**: Modular design allows for easy feature additions

### Future Enhancements
- User authentication and multi-user support
- Cloud storage integration
- Document versioning
- Full-text search within documents
- Reporting and analytics
- Document workflow management

## License

This project is provided as-is for educational and practical use.

---

**Document Management System v1.0**  
Built with Python 3.8+ and Tkinter  
Created by Augment Agent
