"""
Main window interface for the Document Management System.
Provides the primary GUI with modern styling and layout.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import subprocess
import platform
from typing import Dict, List, Optional
import logging

from db.database import DatabaseManager
from models.file_manager import FileManager, DocumentMetadata
from ui.upload_dialog import UploadDialog
from ui.search_dialog import SearchDialog

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MainWindow:
    """Main application window for the Document Management System."""
    
    def __init__(self):
        """Initialize the main window."""
        self.root = tk.Tk()
        self.db_manager = DatabaseManager()
        self.file_manager = FileManager()
        self.document_metadata = DocumentMetadata()
        
        # Current documents list
        self.current_documents = []
        
        self.setup_window()
        self.create_styles()
        self.create_widgets()
        self.load_documents()
    
    def setup_window(self) -> None:
        """Configure the main window properties."""
        self.root.title("Document Management System")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"1200x800+{x}+{y}")
        
        # Configure window icon (if available)
        try:
            # You can add an icon file here
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
    
    def create_styles(self) -> None:
        """Create and configure ttk styles for modern appearance."""
        style = ttk.Style()
        
        # Use a modern theme
        available_themes = style.theme_names()
        if 'clam' in available_themes:
            style.theme_use('clam')
        elif 'alt' in available_themes:
            style.theme_use('alt')
        
        # Configure custom styles
        style.configure('Title.TLabel', font=('Segoe UI', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Segoe UI', 12, 'bold'))
        style.configure('Info.TLabel', font=('Segoe UI', 9))
        
        # Button styles
        style.configure('Action.TButton', font=('Segoe UI', 10))
        style.configure('Primary.TButton', font=('Segoe UI', 10, 'bold'))
        
        # Treeview styles
        style.configure('Documents.Treeview', font=('Segoe UI', 9))
        style.configure('Documents.Treeview.Heading', font=('Segoe UI', 10, 'bold'))
    
    def create_widgets(self) -> None:
        """Create and layout all GUI widgets."""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Document Management System", 
                               style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Left panel - Controls
        left_panel = ttk.Frame(main_frame, padding="10")
        left_panel.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Upload section
        upload_frame = ttk.LabelFrame(left_panel, text="File Operations", padding="10")
        upload_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.upload_btn = ttk.Button(upload_frame, text="Upload Document", 
                                    command=self.upload_document, 
                                    style='Primary.TButton')
        self.upload_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.refresh_btn = ttk.Button(upload_frame, text="Refresh List", 
                                     command=self.load_documents,
                                     style='Action.TButton')
        self.refresh_btn.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # Search section
        search_frame = ttk.LabelFrame(left_panel, text="Search Documents", padding="10")
        search_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(search_frame, text="Search:").grid(row=0, column=0, sticky=tk.W)
        
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        self.search_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 5))
        self.search_entry.bind('<KeyRelease>', self.on_search_change)
        
        search_btn_frame = ttk.Frame(search_frame)
        search_btn_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        self.search_btn = ttk.Button(search_btn_frame, text="Search", 
                                    command=self.search_documents,
                                    style='Action.TButton')
        self.search_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        self.clear_search_btn = ttk.Button(search_btn_frame, text="Clear", 
                                          command=self.clear_search,
                                          style='Action.TButton')
        self.clear_search_btn.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        search_btn_frame.columnconfigure(0, weight=1)
        search_btn_frame.columnconfigure(1, weight=1)
        
        # Statistics section
        stats_frame = ttk.LabelFrame(left_panel, text="Statistics", padding="10")
        stats_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.stats_label = ttk.Label(stats_frame, text="Loading...", style='Info.TLabel')
        self.stats_label.grid(row=0, column=0, sticky=tk.W)
        
        # Configure left panel
        left_panel.columnconfigure(0, weight=1)
        upload_frame.columnconfigure(0, weight=1)
        search_frame.columnconfigure(0, weight=1)
        
        # Right panel - Document list
        right_panel = ttk.Frame(main_frame, padding="10")
        right_panel.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_panel.columnconfigure(0, weight=1)
        right_panel.rowconfigure(0, weight=1)
        
        # Documents list
        list_frame = ttk.LabelFrame(right_panel, text="Documents", padding="10")
        list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Create treeview for documents
        columns = ('Title', 'Category', 'Type', 'Size', 'Date')
        self.documents_tree = ttk.Treeview(list_frame, columns=columns, show='headings',
                                          style='Documents.Treeview')
        
        # Configure columns
        self.documents_tree.heading('Title', text='Title')
        self.documents_tree.heading('Category', text='Category')
        self.documents_tree.heading('Type', text='Type')
        self.documents_tree.heading('Size', text='Size')
        self.documents_tree.heading('Date', text='Upload Date')
        
        # Configure column widths
        self.documents_tree.column('Title', width=300, minwidth=200)
        self.documents_tree.column('Category', width=120, minwidth=100)
        self.documents_tree.column('Type', width=100, minwidth=80)
        self.documents_tree.column('Size', width=80, minwidth=60)
        self.documents_tree.column('Date', width=120, minwidth=100)
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, 
                                   command=self.documents_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, 
                                   command=self.documents_tree.xview)
        
        self.documents_tree.configure(yscrollcommand=v_scrollbar.set,
                                     xscrollcommand=h_scrollbar.set)
        
        # Grid treeview and scrollbars
        self.documents_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # Document actions frame
        actions_frame = ttk.Frame(right_panel)
        actions_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.view_btn = ttk.Button(actions_frame, text="View/Open", 
                                  command=self.view_document,
                                  style='Action.TButton')
        self.view_btn.grid(row=0, column=0, padx=(0, 5))
        
        self.delete_btn = ttk.Button(actions_frame, text="Delete", 
                                    command=self.delete_document,
                                    style='Action.TButton')
        self.delete_btn.grid(row=0, column=1, padx=(0, 5))
        
        self.details_btn = ttk.Button(actions_frame, text="Details", 
                                     command=self.show_document_details,
                                     style='Action.TButton')
        self.details_btn.grid(row=0, column=2)
        
        # Bind double-click to view document
        self.documents_tree.bind('<Double-1>', lambda e: self.view_document())
        
        # Bind selection change
        self.documents_tree.bind('<<TreeviewSelect>>', self.on_document_select)
        
        # Initially disable action buttons
        self.update_action_buttons_state(False)
    
    def update_action_buttons_state(self, enabled: bool) -> None:
        """Enable or disable action buttons based on selection."""
        state = 'normal' if enabled else 'disabled'
        self.view_btn.configure(state=state)
        self.delete_btn.configure(state=state)
        self.details_btn.configure(state=state)
    
    def on_document_select(self, event) -> None:
        """Handle document selection change."""
        selection = self.documents_tree.selection()
        self.update_action_buttons_state(len(selection) > 0)
    
    def on_search_change(self, event) -> None:
        """Handle search entry changes."""
        # Auto-search after a short delay (you could implement debouncing here)
        pass
    
    def load_documents(self) -> None:
        """Load and display all documents."""
        try:
            # Clear existing items
            for item in self.documents_tree.get_children():
                self.documents_tree.delete(item)
            
            # Load documents from database
            self.current_documents = self.db_manager.get_all_documents()
            
            # Populate treeview
            for doc in self.current_documents:
                # Format file size
                size_str = self.document_metadata.format_file_size(doc['file_size'])
                
                # Format date
                date_str = doc['upload_date'][:10] if doc['upload_date'] else 'Unknown'
                
                # Insert into treeview
                self.documents_tree.insert('', 'end', values=(
                    doc['title'],
                    doc['category'],
                    doc['file_type'].upper(),
                    size_str,
                    date_str
                ))
            
            # Update statistics
            self.update_statistics()
            
            logger.info(f"Loaded {len(self.current_documents)} documents")
            
        except Exception as e:
            logger.error(f"Error loading documents: {e}")
            messagebox.showerror("Error", f"Failed to load documents: {str(e)}")
    
    def update_statistics(self) -> None:
        """Update the statistics display."""
        try:
            stats = self.db_manager.get_document_stats()
            storage_stats = self.file_manager.get_storage_stats()
            
            stats_text = f"Total Documents: {stats['total_documents']}\n"
            stats_text += f"Storage Used: {storage_stats['total_size_mb']} MB"
            
            self.stats_label.configure(text=stats_text)
            
        except Exception as e:
            logger.error(f"Error updating statistics: {e}")
            self.stats_label.configure(text="Statistics unavailable")
    
    def upload_document(self) -> None:
        """Open the upload document dialog."""
        try:
            dialog = UploadDialog(self.root, self.db_manager, self.file_manager)
            if dialog.result:
                # Refresh the document list
                self.load_documents()
                messagebox.showinfo("Success", "Document uploaded successfully!")
                
        except Exception as e:
            logger.error(f"Error in upload dialog: {e}")
            messagebox.showerror("Error", f"Upload failed: {str(e)}")
    
    def search_documents(self) -> None:
        """Search documents based on the search term."""
        search_term = self.search_var.get().strip()
        
        if not search_term:
            self.load_documents()
            return
        
        try:
            # Clear existing items
            for item in self.documents_tree.get_children():
                self.documents_tree.delete(item)
            
            # Search documents
            self.current_documents = self.db_manager.search_documents(search_term)
            
            # Populate treeview with search results
            for doc in self.current_documents:
                size_str = self.document_metadata.format_file_size(doc['file_size'])
                date_str = doc['upload_date'][:10] if doc['upload_date'] else 'Unknown'
                
                self.documents_tree.insert('', 'end', values=(
                    doc['title'],
                    doc['category'],
                    doc['file_type'].upper(),
                    size_str,
                    date_str
                ))
            
            logger.info(f"Search found {len(self.current_documents)} documents")
            
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            messagebox.showerror("Error", f"Search failed: {str(e)}")
    
    def clear_search(self) -> None:
        """Clear the search and reload all documents."""
        self.search_var.set("")
        self.load_documents()
    
    def get_selected_document(self) -> Optional[Dict]:
        """Get the currently selected document."""
        selection = self.documents_tree.selection()
        if not selection:
            return None
        
        # Get the index of the selected item
        item = selection[0]
        index = self.documents_tree.index(item)
        
        if 0 <= index < len(self.current_documents):
            return self.current_documents[index]
        
        return None
    
    def view_document(self) -> None:
        """Open the selected document with the system default application."""
        document = self.get_selected_document()
        if not document:
            messagebox.showwarning("Warning", "Please select a document to view.")
            return
        
        try:
            file_path = document['file_path']
            
            if not os.path.exists(file_path):
                messagebox.showerror("Error", "File not found. It may have been moved or deleted.")
                return
            
            # Open file with system default application
            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', file_path])
            else:  # Linux
                subprocess.run(['xdg-open', file_path])
            
            logger.info(f"Opened document: {document['title']}")
            
        except Exception as e:
            logger.error(f"Error opening document: {e}")
            messagebox.showerror("Error", f"Failed to open document: {str(e)}")
    
    def delete_document(self) -> None:
        """Delete the selected document."""
        document = self.get_selected_document()
        if not document:
            messagebox.showwarning("Warning", "Please select a document to delete.")
            return
        
        # Confirm deletion
        result = messagebox.askyesno(
            "Confirm Deletion",
            f"Are you sure you want to delete '{document['title']}'?\n\n"
            "This action cannot be undone."
        )
        
        if not result:
            return
        
        try:
            # Delete from database
            success = self.db_manager.delete_document(document['id'])
            
            if success:
                # Delete physical file
                self.file_manager.delete_stored_file(document['file_path'])
                
                # Refresh document list
                self.load_documents()
                
                messagebox.showinfo("Success", "Document deleted successfully!")
                logger.info(f"Deleted document: {document['title']}")
            else:
                messagebox.showerror("Error", "Failed to delete document from database.")
                
        except Exception as e:
            logger.error(f"Error deleting document: {e}")
            messagebox.showerror("Error", f"Failed to delete document: {str(e)}")
    
    def show_document_details(self) -> None:
        """Show detailed information about the selected document."""
        document = self.get_selected_document()
        if not document:
            messagebox.showwarning("Warning", "Please select a document to view details.")
            return
        
        # Create details window
        details_window = tk.Toplevel(self.root)
        details_window.title(f"Document Details - {document['title']}")
        details_window.geometry("500x400")
        details_window.resizable(False, False)
        
        # Center the window
        details_window.transient(self.root)
        details_window.grab_set()
        
        # Create content
        frame = ttk.Frame(details_window, padding="20")
        frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Document details
        details = [
            ("Title:", document['title']),
            ("Description:", document['description'] or 'No description'),
            ("Category:", document['category']),
            ("File Name:", document['file_name']),
            ("File Type:", document['file_type'].upper()),
            ("File Size:", self.document_metadata.format_file_size(document['file_size'])),
            ("Upload Date:", document['upload_date']),
            ("File Path:", document['file_path'])
        ]
        
        for i, (label, value) in enumerate(details):
            ttk.Label(frame, text=label, style='Heading.TLabel').grid(
                row=i, column=0, sticky=tk.W, pady=2)
            ttk.Label(frame, text=str(value), style='Info.TLabel').grid(
                row=i, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Close button
        ttk.Button(frame, text="Close", 
                  command=details_window.destroy).grid(
                      row=len(details), column=0, columnspan=2, pady=(20, 0))
    
    def run(self) -> None:
        """Start the main application loop."""
        try:
            logger.info("Starting Document Management System")
            self.root.mainloop()
        except Exception as e:
            logger.error(f"Application error: {e}")
            messagebox.showerror("Application Error", f"An error occurred: {str(e)}")
        finally:
            logger.info("Document Management System closed")
