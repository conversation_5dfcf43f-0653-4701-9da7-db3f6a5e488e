2025-09-06 02:33:15,819 - __main__ - INFO - Starting Document Management System
2025-09-06 02:33:15,837 - __main__ - INFO - All dependencies are available
2025-09-06 02:33:15,838 - __main__ - INFO - Directory ensured: E:\project\pythonP\DMS\storage
2025-09-06 02:33:15,838 - __main__ - INFO - Directory ensured: E:\project\pythonP\DMS\db
2025-09-06 02:33:15,839 - __main__ - INFO - Directory ensured: E:\project\pythonP\DMS\ui
2025-09-06 02:33:15,839 - __main__ - INFO - Directory ensured: E:\project\pythonP\DMS\models
2025-09-06 02:33:16,051 - db.database - INFO - Database initialized successfully
2025-09-06 02:33:16,051 - models.file_manager - INFO - Storage directory ensured: E:\project\pythonP\DMS\storage
2025-09-06 02:33:16,129 - db.database - INFO - Retrieved 0 documents
2025-09-06 02:33:16,130 - ui.main_window - INFO - Loaded 0 documents
2025-09-06 02:33:16,130 - ui.main_window - INFO - Starting Document Management System
2025-09-06 02:34:05,399 - models.file_manager - INFO - File stored successfully: مخطط يومي_20250906_023405.pdf
2025-09-06 02:34:05,416 - db.database - INFO - Document added with ID: 1
2025-09-06 02:34:05,417 - ui.upload_dialog - INFO - Document uploaded successfully: مخطط يومي
2025-09-06 02:34:06,429 - db.database - INFO - Retrieved 1 documents
2025-09-06 02:34:06,432 - ui.main_window - INFO - Loaded 1 documents
2025-09-06 02:34:16,929 - ui.main_window - INFO - Document Management System closed
2025-09-06 02:34:16,930 - __main__ - INFO - Document Management System closed normally
2025-09-06 02:44:46,881 - __main__ - INFO - Starting Document Management System
2025-09-06 02:44:46,935 - __main__ - INFO - All dependencies are available
2025-09-06 02:44:46,935 - __main__ - INFO - Directory ensured: e:\project\pythonP\DMS\storage
2025-09-06 02:44:46,935 - __main__ - INFO - Directory ensured: e:\project\pythonP\DMS\db
2025-09-06 02:44:46,936 - __main__ - INFO - Directory ensured: e:\project\pythonP\DMS\ui
2025-09-06 02:44:46,936 - __main__ - INFO - Directory ensured: e:\project\pythonP\DMS\models
2025-09-06 02:44:47,217 - db.database - INFO - Database initialized successfully
2025-09-06 02:44:47,217 - models.file_manager - INFO - Storage directory ensured: E:\project\pythonP\DMS\storage
2025-09-06 02:44:47,275 - db.database - INFO - Retrieved 1 documents
2025-09-06 02:44:47,277 - ui.main_window - INFO - Loaded 1 documents
2025-09-06 02:44:47,277 - ui.main_window - INFO - Starting Document Management System
2025-09-06 02:44:54,784 - db.database - INFO - Found 1 documents matching 'مخطط'
2025-09-06 02:44:54,785 - ui.main_window - INFO - Search found 1 documents
2025-09-06 02:44:57,286 - db.database - INFO - Retrieved 1 documents
2025-09-06 02:44:57,293 - ui.main_window - INFO - Loaded 1 documents
2025-09-06 02:45:36,903 - models.file_manager - INFO - File stored successfully: جدول الدراسة اليومية_20250906_024536.pdf
2025-09-06 02:45:36,921 - db.database - INFO - Document added with ID: 2
2025-09-06 02:45:36,923 - ui.upload_dialog - INFO - Document uploaded successfully: جدول الدراسة اليومية
2025-09-06 02:45:37,939 - db.database - INFO - Retrieved 2 documents
2025-09-06 02:45:37,942 - ui.main_window - INFO - Loaded 2 documents
2025-09-06 02:45:47,660 - db.database - INFO - Found 1 documents matching 'جدول'
2025-09-06 02:45:47,662 - ui.main_window - INFO - Search found 1 documents
2025-09-06 02:45:51,301 - db.database - INFO - Retrieved 2 documents
2025-09-06 02:45:51,313 - ui.main_window - INFO - Loaded 2 documents
2025-09-06 02:46:13,841 - db.database - INFO - Retrieved 2 documents
2025-09-06 02:46:13,847 - ui.main_window - INFO - Loaded 2 documents
2025-09-06 02:46:33,638 - ui.main_window - INFO - Opened document: مخطط يومي
2025-09-06 02:46:39,609 - ui.main_window - INFO - Opened document: جدول الدراسة اليومية
2025-09-06 02:46:54,964 - ui.main_window - INFO - Document Management System closed
2025-09-06 02:46:54,965 - __main__ - INFO - Document Management System closed normally
