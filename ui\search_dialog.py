"""
Search dialog for the Document Management System.
Provides advanced search functionality with multiple criteria.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Optional
import logging

from db.database import DatabaseManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SearchDialog:
    """Advanced search dialog for documents."""
    
    def __init__(self, parent, db_manager: DatabaseManager):
        """
        Initialize the search dialog.
        
        Args:
            parent: Parent window
            db_manager: Database manager instance
        """
        self.parent = parent
        self.db_manager = db_manager
        
        # Dialog result
        self.result = None
        self.search_results = []
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.setup_dialog()
        self.create_widgets()
        
        # Show dialog
        self.dialog.transient(parent)
        self.dialog.grab_set()
        self.dialog.focus_set()
        
        # Wait for dialog to close
        parent.wait_window(self.dialog)
    
    def setup_dialog(self) -> None:
        """Configure the dialog window properties."""
        self.dialog.title("Advanced Search")
        self.dialog.geometry("700x600")
        self.dialog.resizable(True, True)
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (700 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"700x600+{x}+{y}")
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.close_dialog)
    
    def create_widgets(self) -> None:
        """Create and layout all dialog widgets."""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Advanced Document Search", 
                               font=('Segoe UI', 14, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # Search criteria section
        criteria_frame = ttk.LabelFrame(main_frame, text="Search Criteria", padding="15")
        criteria_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        criteria_frame.columnconfigure(1, weight=1)
        
        # Title search
        ttk.Label(criteria_frame, text="Title contains:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.title_var = tk.StringVar()
        self.title_entry = ttk.Entry(criteria_frame, textvariable=self.title_var, font=('Segoe UI', 10))
        self.title_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 10), padx=(10, 0))
        
        # Description search
        ttk.Label(criteria_frame, text="Description contains:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.description_var = tk.StringVar()
        self.description_entry = ttk.Entry(criteria_frame, textvariable=self.description_var, font=('Segoe UI', 10))
        self.description_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 10), padx=(10, 0))
        
        # Category filter
        ttk.Label(criteria_frame, text="Category:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(criteria_frame, textvariable=self.category_var,
                                          font=('Segoe UI', 10), state="readonly")
        self.category_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 10), padx=(10, 0))
        
        # File type filter
        ttk.Label(criteria_frame, text="File Type:").grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        self.file_type_var = tk.StringVar()
        self.file_type_combo = ttk.Combobox(criteria_frame, textvariable=self.file_type_var,
                                           font=('Segoe UI', 10), state="readonly")
        self.file_type_combo.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=(0, 10), padx=(10, 0))
        
        # Load filter options
        self.load_filter_options()
        
        # Search buttons
        button_frame = ttk.Frame(criteria_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))
        
        self.search_btn = ttk.Button(button_frame, text="Search", 
                                    command=self.perform_search,
                                    style='Primary.TButton')
        self.search_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.clear_btn = ttk.Button(button_frame, text="Clear", 
                                   command=self.clear_search)
        self.clear_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.show_all_btn = ttk.Button(button_frame, text="Show All", 
                                      command=self.show_all_documents)
        self.show_all_btn.grid(row=0, column=2)
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="Search Results", padding="15")
        results_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Results treeview
        columns = ('Title', 'Category', 'Type', 'Size', 'Date')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings',
                                        style='Documents.Treeview')
        
        # Configure columns
        self.results_tree.heading('Title', text='Title')
        self.results_tree.heading('Category', text='Category')
        self.results_tree.heading('Type', text='Type')
        self.results_tree.heading('Size', text='Size')
        self.results_tree.heading('Date', text='Upload Date')
        
        # Configure column widths
        self.results_tree.column('Title', width=250, minwidth=200)
        self.results_tree.column('Category', width=100, minwidth=80)
        self.results_tree.column('Type', width=80, minwidth=60)
        self.results_tree.column('Size', width=80, minwidth=60)
        self.results_tree.column('Date', width=100, minwidth=80)
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, 
                                   command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, 
                                   command=self.results_tree.xview)
        
        self.results_tree.configure(yscrollcommand=v_scrollbar.set,
                                   xscrollcommand=h_scrollbar.set)
        
        # Grid treeview and scrollbars
        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # Results info
        self.results_info_var = tk.StringVar(value="Enter search criteria and click Search")
        self.results_info_label = ttk.Label(results_frame, textvariable=self.results_info_var,
                                           font=('Segoe UI', 9), foreground="blue")
        self.results_info_label.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))
        
        # Dialog buttons
        dialog_button_frame = ttk.Frame(main_frame)
        dialog_button_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        
        self.select_btn = ttk.Button(dialog_button_frame, text="Select & Close", 
                                    command=self.select_and_close,
                                    style='Primary.TButton')
        self.select_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.close_btn = ttk.Button(dialog_button_frame, text="Close", 
                                   command=self.close_dialog)
        self.close_btn.grid(row=0, column=1)
        
        # Initially disable select button
        self.select_btn.configure(state='disabled')
        
        # Bind events
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_select)
        self.results_tree.bind('<Double-1>', lambda e: self.select_and_close())
        
        # Bind Enter key to search
        self.title_entry.bind('<Return>', lambda e: self.perform_search())
        self.description_entry.bind('<Return>', lambda e: self.perform_search())
    
    def load_filter_options(self) -> None:
        """Load available filter options from the database."""
        try:
            # Load categories
            categories = self.db_manager.get_categories()
            category_names = ['All Categories'] + [cat['name'] for cat in categories]
            self.category_combo['values'] = category_names
            self.category_combo.set('All Categories')
            
            # Load file types
            # This would ideally come from the database, but we'll use predefined types
            file_types = ['All Types', '.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.gif', '.bmp']
            self.file_type_combo['values'] = file_types
            self.file_type_combo.set('All Types')
            
        except Exception as e:
            logger.error(f"Error loading filter options: {e}")
            # Fallback options
            self.category_combo['values'] = ['All Categories', 'General', 'Reports', 'Contracts', 'Images', 'Archive']
            self.category_combo.set('All Categories')
            self.file_type_combo['values'] = ['All Types', '.pdf', '.doc', '.docx', '.jpg', '.png']
            self.file_type_combo.set('All Types')
    
    def perform_search(self) -> None:
        """Perform the search based on current criteria."""
        try:
            # Get search criteria
            title_term = self.title_var.get().strip()
            description_term = self.description_var.get().strip()
            category_filter = self.category_var.get()
            file_type_filter = self.file_type_var.get()
            
            # Build search term
            search_terms = []
            if title_term:
                search_terms.append(title_term)
            if description_term:
                search_terms.append(description_term)
            
            search_term = ' '.join(search_terms)
            
            # Perform search
            if search_term:
                self.search_results = self.db_manager.search_documents(search_term)
            else:
                self.search_results = self.db_manager.get_all_documents()
            
            # Apply filters
            if category_filter and category_filter != 'All Categories':
                self.search_results = [doc for doc in self.search_results 
                                     if doc['category'] == category_filter]
            
            if file_type_filter and file_type_filter != 'All Types':
                self.search_results = [doc for doc in self.search_results 
                                     if doc['file_type'] == file_type_filter]
            
            # Display results
            self.display_results()
            
            logger.info(f"Search completed: {len(self.search_results)} results found")
            
        except Exception as e:
            logger.error(f"Error performing search: {e}")
            messagebox.showerror("Search Error", f"Search failed: {str(e)}")
    
    def display_results(self) -> None:
        """Display search results in the treeview."""
        # Clear existing results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # Add results
        for doc in self.search_results:
            # Format file size
            size_bytes = doc.get('file_size', 0)
            if size_bytes == 0:
                size_str = "0 B"
            else:
                size_names = ["B", "KB", "MB", "GB"]
                i = 0
                size = float(size_bytes)
                while size >= 1024.0 and i < len(size_names) - 1:
                    size /= 1024.0
                    i += 1
                size_str = f"{size:.1f} {size_names[i]}"
            
            # Format date
            date_str = doc['upload_date'][:10] if doc['upload_date'] else 'Unknown'
            
            # Insert into treeview
            self.results_tree.insert('', 'end', values=(
                doc['title'],
                doc['category'],
                doc['file_type'].upper(),
                size_str,
                date_str
            ))
        
        # Update results info
        count = len(self.search_results)
        if count == 0:
            self.results_info_var.set("No documents found matching the search criteria")
        elif count == 1:
            self.results_info_var.set("1 document found")
        else:
            self.results_info_var.set(f"{count} documents found")
    
    def show_all_documents(self) -> None:
        """Show all documents."""
        try:
            self.search_results = self.db_manager.get_all_documents()
            self.display_results()
            
        except Exception as e:
            logger.error(f"Error loading all documents: {e}")
            messagebox.showerror("Error", f"Failed to load documents: {str(e)}")
    
    def clear_search(self) -> None:
        """Clear all search criteria."""
        self.title_var.set("")
        self.description_var.set("")
        self.category_var.set("All Categories")
        self.file_type_var.set("All Types")
        
        # Clear results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        self.search_results = []
        self.results_info_var.set("Enter search criteria and click Search")
        self.select_btn.configure(state='disabled')
    
    def on_result_select(self, event) -> None:
        """Handle result selection change."""
        selection = self.results_tree.selection()
        self.select_btn.configure(state='normal' if selection else 'disabled')
    
    def get_selected_document(self) -> Optional[Dict]:
        """Get the currently selected document."""
        selection = self.results_tree.selection()
        if not selection:
            return None
        
        # Get the index of the selected item
        item = selection[0]
        index = self.results_tree.index(item)
        
        if 0 <= index < len(self.search_results):
            return self.search_results[index]
        
        return None
    
    def select_and_close(self) -> None:
        """Select the current document and close dialog."""
        selected_doc = self.get_selected_document()
        if selected_doc:
            self.result = {
                'selected_document': selected_doc,
                'all_results': self.search_results
            }
            self.dialog.destroy()
        else:
            messagebox.showwarning("Warning", "Please select a document from the search results.")
    
    def close_dialog(self) -> None:
        """Close the dialog without selection."""
        self.result = None
        self.dialog.destroy()
