"""
Database operations for the Document Management System.
Handles SQLite database creation, connection management, and CRUD operations.
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages SQLite database operations for the DMS."""
    
    def __init__(self, db_path: str = "dms.db"):
        """
        Initialize database manager.
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """
        Get a database connection with row factory for dict-like access.
        
        Returns:
            SQLite connection object
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self) -> None:
        """Initialize the database with required tables."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Create documents table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS documents (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        description TEXT,
                        category TEXT,
                        file_name TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        file_size INTEGER,
                        file_type TEXT,
                        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create categories table for future use
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS categories (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT UNIQUE NOT NULL,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Insert default categories
                default_categories = [
                    ('General', 'General documents'),
                    ('Reports', 'Business reports and analytics'),
                    ('Contracts', 'Legal contracts and agreements'),
                    ('Images', 'Image files and graphics'),
                    ('Archive', 'Archived documents')
                ]
                
                cursor.executemany('''
                    INSERT OR IGNORE INTO categories (name, description) 
                    VALUES (?, ?)
                ''', default_categories)
                
                conn.commit()
                logger.info("Database initialized successfully")
                
        except sqlite3.Error as e:
            logger.error(f"Database initialization error: {e}")
            raise
    
    def add_document(self, title: str, description: str, category: str, 
                    file_name: str, file_path: str, file_size: int, 
                    file_type: str) -> int:
        """
        Add a new document to the database.
        
        Args:
            title: Document title
            description: Document description
            category: Document category
            file_name: Original file name
            file_path: Path to stored file
            file_size: File size in bytes
            file_type: File extension/type
            
        Returns:
            ID of the inserted document
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO documents 
                    (title, description, category, file_name, file_path, 
                     file_size, file_type, upload_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (title, description, category, file_name, file_path, 
                      file_size, file_type, datetime.now()))
                
                document_id = cursor.lastrowid
                conn.commit()
                logger.info(f"Document added with ID: {document_id}")
                return document_id
                
        except sqlite3.Error as e:
            logger.error(f"Error adding document: {e}")
            raise
    
    def get_all_documents(self) -> List[Dict]:
        """
        Retrieve all documents from the database.
        
        Returns:
            List of document dictionaries
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM documents 
                    ORDER BY upload_date DESC
                ''')
                
                documents = [dict(row) for row in cursor.fetchall()]
                logger.info(f"Retrieved {len(documents)} documents")
                return documents
                
        except sqlite3.Error as e:
            logger.error(f"Error retrieving documents: {e}")
            return []
    
    def search_documents(self, search_term: str) -> List[Dict]:
        """
        Search documents by title and description.
        
        Args:
            search_term: Term to search for
            
        Returns:
            List of matching document dictionaries
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                search_pattern = f"%{search_term}%"
                cursor.execute('''
                    SELECT * FROM documents 
                    WHERE title LIKE ? OR description LIKE ?
                    ORDER BY upload_date DESC
                ''', (search_pattern, search_pattern))
                
                documents = [dict(row) for row in cursor.fetchall()]
                logger.info(f"Found {len(documents)} documents matching '{search_term}'")
                return documents
                
        except sqlite3.Error as e:
            logger.error(f"Error searching documents: {e}")
            return []
    
    def get_document_by_id(self, document_id: int) -> Optional[Dict]:
        """
        Get a specific document by ID.
        
        Args:
            document_id: Document ID
            
        Returns:
            Document dictionary or None if not found
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM documents WHERE id = ?', (document_id,))
                
                row = cursor.fetchone()
                if row:
                    return dict(row)
                return None
                
        except sqlite3.Error as e:
            logger.error(f"Error retrieving document {document_id}: {e}")
            return None
    
    def delete_document(self, document_id: int) -> bool:
        """
        Delete a document from the database.
        
        Args:
            document_id: Document ID to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM documents WHERE id = ?', (document_id,))
                
                if cursor.rowcount > 0:
                    conn.commit()
                    logger.info(f"Document {document_id} deleted successfully")
                    return True
                else:
                    logger.warning(f"Document {document_id} not found for deletion")
                    return False
                    
        except sqlite3.Error as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            return False
    
    def get_categories(self) -> List[Dict]:
        """
        Get all available categories.
        
        Returns:
            List of category dictionaries
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM categories ORDER BY name')
                
                categories = [dict(row) for row in cursor.fetchall()]
                return categories
                
        except sqlite3.Error as e:
            logger.error(f"Error retrieving categories: {e}")
            return []
    
    def get_document_stats(self) -> Dict:
        """
        Get statistics about documents in the database.
        
        Returns:
            Dictionary with document statistics
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Total documents
                cursor.execute('SELECT COUNT(*) as total FROM documents')
                total = cursor.fetchone()['total']
                
                # Documents by category
                cursor.execute('''
                    SELECT category, COUNT(*) as count 
                    FROM documents 
                    GROUP BY category 
                    ORDER BY count DESC
                ''')
                by_category = [dict(row) for row in cursor.fetchall()]
                
                # Documents by file type
                cursor.execute('''
                    SELECT file_type, COUNT(*) as count 
                    FROM documents 
                    GROUP BY file_type 
                    ORDER BY count DESC
                ''')
                by_type = [dict(row) for row in cursor.fetchall()]
                
                return {
                    'total_documents': total,
                    'by_category': by_category,
                    'by_file_type': by_type
                }
                
        except sqlite3.Error as e:
            logger.error(f"Error retrieving document stats: {e}")
            return {'total_documents': 0, 'by_category': [], 'by_file_type': []}
